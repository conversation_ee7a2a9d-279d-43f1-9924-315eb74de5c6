import 'package:flutter_test/flutter_test.dart';
import 'package:rideoon/services/auth_service.dart';
import 'package:rideoon/models/auth/sign_up_request.dart';
import 'package:rideoon/models/auth/sign_up_response.dart';
import 'package:rideoon/models/api_response.dart';

void main() {
  group('AuthService Tests', () {
    test('SignUpRequest should serialize to JSON correctly', () {
      final request = SignUpRequest(
        email: '<EMAIL>',
        password: 'password123',
        repeatedPassword: 'password123',
        firstName: 'John',
        lastName: 'Doe',
        phoneNumber: '1234567890',
      );

      final json = request.toJson();

      expect(json['email'], equals('<EMAIL>'));
      expect(json['password'], equals('password123'));
      expect(json['repeatedPassword'], equals('password123'));
      expect(json['firstName'], equals('John'));
      expect(json['lastName'], equals('Doe'));
      expect(json['phoneNumber'], equals('1234567890'));
    });

    test('SignUpResponse should deserialize from JSON correctly', () {
      final json = {
        'success': true,
        'message': 'User created successfully',
        'userId': '12345',
        'token': 'abc123',
      };

      final response = SignUpResponse.fromJson(json);

      expect(response.success, isTrue);
      expect(response.message, equals('User created successfully'));
      expect(response.userId, equals('12345'));
      expect(response.token, equals('abc123'));
    });

    test('ApiResponse should handle success correctly', () {
      final response = ApiResponse<String>.success(
        message: 'Success',
        data: 'test data',
        statusCode: 200,
      );

      expect(response.success, isTrue);
      expect(response.message, equals('Success'));
      expect(response.data, equals('test data'));
      expect(response.statusCode, equals(200));
    });

    test('ApiResponse should handle error correctly', () {
      final response = ApiResponse<String>.error(
        message: 'Error occurred',
        statusCode: 400,
      );

      expect(response.success, isFalse);
      expect(response.message, equals('Error occurred'));
      expect(response.data, isNull);
      expect(response.statusCode, equals(400));
    });

    test('AuthService email validation should work correctly', () {
      expect(AuthService.isValidEmail('<EMAIL>'), isTrue);
      expect(AuthService.isValidEmail('<EMAIL>'), isTrue);
      expect(AuthService.isValidEmail('invalid-email'), isFalse);
      expect(AuthService.isValidEmail('test@'), isFalse);
      expect(AuthService.isValidEmail('@example.com'), isFalse);
    });

    test('AuthService phone validation should work correctly', () {
      expect(AuthService.isValidPhoneNumber('1234567890'), isTrue);
      expect(AuthService.isValidPhoneNumber('+1234567890'), isTrue);
      expect(AuthService.isValidPhoneNumber('************'), isTrue);
      expect(AuthService.isValidPhoneNumber('(*************'), isTrue);
      expect(AuthService.isValidPhoneNumber('123'), isFalse);
      expect(AuthService.isValidPhoneNumber('abc'), isFalse);
    });

    test('AuthService password validation should work correctly', () {
      expect(AuthService.isValidPassword('password123'), isTrue);
      expect(AuthService.isValidPassword('123456'), isTrue);
      expect(AuthService.isValidPassword('12345'), isFalse);
      expect(AuthService.isValidPassword(''), isFalse);
    });
  });
}
