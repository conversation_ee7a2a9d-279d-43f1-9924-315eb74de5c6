# Shipment Models Documentation

## Overview

This document describes the comprehensive model system for the RideOn shipment functionality, including core models, shipment tracking, payment processing, and API integration.

## Model Architecture

The shipment system is organized into four main categories:

### 🏗️ **Core Models** (`lib/models/core/`)
- **Address**: Location information with coordinates
- **Contact**: Person contact details with formatting

### 📦 **Shipment Models** (`lib/models/shipment/`)
- **Package**: Individual package/cargo details
- **ShipmentStatus**: Status tracking with UI colors and icons
- **TrackingEvent**: Individual tracking events
- **TrackingTimeline**: Complete tracking history
- **Shipment**: Main shipment entity

### 💳 **Payment Models** (`lib/models/payment/`)
- **PaymentBreakdown**: Cost breakdown with taxes and fees
- **Payment**: Payment transaction details

### 🌐 **API Models** (`lib/models/api/`)
- **Request Models**: For backend API calls
- **Response Models**: For backend API responses

## Core Models

### Address Model
```dart
class Address {
  final String street;
  final String? landmark;
  final String city;
  final String state;
  final String? postalCode;
  final String country;
  final double? latitude;
  final double? longitude;
}
```

**Key Features:**
- ✅ Full address formatting (`fullAddress`, `shortAddress`)
- ✅ GPS coordinates support
- ✅ Nigeria-focused with international support
- ✅ JSON serialization/deserialization

### Contact Model
```dart
class Contact {
  final String name;
  final String phoneNumber;
  final String? email;
  final String? alternatePhone;
}
```

**Key Features:**
- ✅ Display name extraction
- ✅ Initials generation for avatars
- ✅ Phone number formatting (+234 support)
- ✅ Email validation support

## Shipment Models

### Package Model
```dart
class Package {
  final String itemName;
  final PackageCategory category;
  final String itemType;
  final double weight;
  final int quantity;
  final PackageDurability durability;
  final DeliveryType deliveryType;
  final List<String> imagePaths;
  final double? value;
}
```

**Package Categories:**
- Electronics, Computer Accessories
- Clothing & Fashion, Books & Documents
- Food & Beverages, Home & Garden
- Sports & Outdoors, Health & Beauty
- And more...

**Durability Levels:**
- Fragile, Average, Durable

**Delivery Types:**
- Standard (3 days), Express (2 days)
- Overnight (1 day), Same Day (8 hours)

### ShipmentStatus Enum
```dart
enum ShipmentStatus {
  pending, confirmed, pickupScheduled,
  pickupInProgress, pickedUp, inTransit,
  atSortingFacility, outForDelivery,
  delivered, completed, cancelled
}
```

**Key Features:**
- ✅ UI colors and icons for each status
- ✅ Status workflow validation
- ✅ Progress calculation
- ✅ Business logic (canBeCancelled, canBeTracked)

### Tracking System
```dart
class TrackingEvent {
  final ShipmentStatus status;
  final String title;
  final DateTime timestamp;
  final Address? location;
  final String? driverName;
}

class TrackingTimeline {
  final List<TrackingEvent> events;
  final ShipmentStatus currentStatus;
  final double? estimatedProgress;
}
```

**Key Features:**
- ✅ Real-time tracking events
- ✅ Driver information
- ✅ Location tracking
- ✅ Progress percentage calculation
- ✅ Timeline sorting and filtering

### Main Shipment Model
```dart
class Shipment {
  final String id;
  final String trackingNumber;
  final Contact sender;
  final Address pickupAddress;
  final Contact receiver;
  final Address deliveryAddress;
  final List<Package> packages;
  final ShipmentStatus status;
  final Payment? payment;
  final TrackingTimeline? tracking;
}
```

**Key Features:**
- ✅ Complete shipment lifecycle
- ✅ Multiple packages support
- ✅ Estimated delivery calculation
- ✅ Overdue detection
- ✅ Legacy format compatibility

## Payment Models

### PaymentBreakdown Model
```dart
class PaymentBreakdown {
  final double baseShippingCost;
  final double vat;
  final double insurance;
  final double pickupCharge;
  final double deliveryCharge;
  final double serviceFee;
  final double discount;
  final bool isInsuranceFree;
  final bool isPickupFree;
  final bool isDeliveryFree;
}
```

**Key Features:**
- ✅ Detailed cost breakdown
- ✅ Free service tracking
- ✅ Total savings calculation
- ✅ VAT and tax support

### Payment Model
```dart
class Payment {
  final String id;
  final PaymentBreakdown breakdown;
  final PaymentMethod method;
  final PaymentStatus status;
  final String? transactionReference;
}
```

**Payment Methods:**
- Cash, Card, Bank Transfer
- Wallet, PayPal, Apple Pay, Google Pay

**Payment Status:**
- Pending, Processing, Completed
- Failed, Cancelled, Refunded

## API Integration

### Request Models
```dart
class CreateShipmentRequest {
  final String senderName;
  final String senderPhone;
  final String pickupAddress;
  final String receiverName;
  final String receiverPhone;
  final String deliveryAddress;
  final List<PackageRequest> packages;
  final PaymentRequest payment;
}
```

### Response Models
```dart
class CreateShipmentResponse {
  final bool success;
  final String message;
  final String? shipmentId;
  final String? trackingNumber;
  final Shipment? shipment;
}
```

## Usage Examples

### Creating a Shipment
```dart
final shipment = Shipment(
  id: 'SHP001',
  trackingNumber: 'RO123456789',
  sender: Contact(
    name: 'John Doe',
    phoneNumber: '***********',
    email: '<EMAIL>',
  ),
  pickupAddress: Address(
    street: '123 Main Street',
    city: 'Lagos',
    state: 'Lagos',
    country: 'Nigeria',
  ),
  receiver: Contact(
    name: 'Jane Smith',
    phoneNumber: '08087654321',
  ),
  deliveryAddress: Address(
    street: '456 Oak Avenue',
    city: 'Abuja',
    state: 'FCT',
    country: 'Nigeria',
  ),
  packages: [
    Package(
      itemName: 'Laptop',
      category: PackageCategory.electronics,
      itemType: 'Computer',
      weight: 2.5,
      durability: PackageDurability.fragile,
      deliveryType: DeliveryType.express,
    ),
  ],
  status: ShipmentStatus.pending,
  createdAt: DateTime.now(),
);
```

### Tracking a Shipment
```dart
final tracking = TrackingTimeline(
  shipmentId: 'SHP001',
  events: [
    TrackingEvent(
      id: 'EVT001',
      status: ShipmentStatus.confirmed,
      title: 'Order Confirmed',
      timestamp: DateTime.now(),
    ),
    TrackingEvent(
      id: 'EVT002',
      status: ShipmentStatus.pickedUp,
      title: 'Package Picked Up',
      timestamp: DateTime.now().add(Duration(hours: 2)),
      location: Address(
        street: '123 Main Street',
        city: 'Lagos',
        state: 'Lagos',
      ),
      driverName: 'Mike Johnson',
      driverPhone: '08011111111',
    ),
  ],
  currentStatus: ShipmentStatus.pickedUp,
  lastUpdated: DateTime.now(),
);

print('Progress: ${tracking.progressPercentage}%');
print('Latest: ${tracking.latestEvent?.title}');
```

### Payment Processing
```dart
final payment = Payment(
  id: 'PAY001',
  shipmentId: 'SHP001',
  breakdown: PaymentBreakdown(
    baseShippingCost: 5000.0,
    vat: 750.0,
    insurance: 0.0, // Free
    pickupCharge: 0.0, // Free
    deliveryCharge: 1000.0,
    serviceFee: 250.0,
    discount: 500.0,
    isInsuranceFree: true,
    isPickupFree: true,
  ),
  method: PaymentMethod.card,
  status: PaymentStatus.completed,
  createdAt: DateTime.now(),
);

print('Total: ₦${payment.breakdown.total}');
print('Savings: ₦${payment.breakdown.totalSavings}');
```

## Integration with UI Components

### ShipmentCard Widget
```dart
ShipmentCard(
  shipment: shipment,
  onTap: () => Navigator.push(
    context,
    MaterialPageRoute(
      builder: (context) => ShipmentDetailsView(shipment: shipment),
    ),
  ),
)
```

### Checkout Flow
```dart
CheckoutView(
  packages: packages,
  pickupAddress: pickupAddress,
  deliveryAddress: deliveryAddress,
  onPaymentComplete: (payment) {
    // Create shipment with payment
    final shipment = Shipment(
      // ... other fields
      payment: payment,
    );
  },
)
```

## Benefits

✅ **Type Safety**: Full type safety with proper enums and models
✅ **Backward Compatibility**: Supports legacy JSON formats
✅ **Extensibility**: Easy to add new fields and features
✅ **UI Integration**: Built-in support for colors, icons, and formatting
✅ **API Ready**: Request/response models for backend integration
✅ **Business Logic**: Built-in validation and workflow rules
✅ **Testing**: Easy to mock and test with clear interfaces

This comprehensive model system provides a solid foundation for the entire shipment functionality in the RideOn app.
